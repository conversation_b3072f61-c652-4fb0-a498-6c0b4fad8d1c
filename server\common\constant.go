package g

// 服务器区域类型
const (
	SERVER_AREA_CHINA = "china" //国内
	SERVER_AREA_HK    = "hk"    //香港
)

// 登录类型
const (
	LOGIN_TYPE_GUEST    = "guest"
	LOGIN_TYPE_WX       = "wx"
	LOGIN_TYPE_ACCOUNT  = "account"
	LOGIN_TYPE_GOOGLE   = "google"
	LOGIN_TYPE_APPLE    = "apple"
	LOGIN_TYPE_FACEBOOK = "facebook"
	LOGIN_TYPE_TWITTER  = "twitter"
	LOGIN_TYPE_LINE     = "line"
)

const (
	LOGIN_ID_TYPE_GUEST    = "guest_id"
	LOGIN_ID_TYPE_WX       = "openid"
	LOGIN_ID_TYPE_ACCOUNT  = "account"
	LOGIN_ID_TYPE_GOOGLE   = "google_openid"
	LOGIN_ID_TYPE_APPLE    = "apple_openid"
	LOGIN_ID_TYPE_FACEBOOK = "facebook_openid"
	LOGIN_ID_TYPE_TWITTER  = "twitter_openid"
	LOGIN_ID_TYPE_LINE     = "line_openid"
)

// 数据库
const (
	DB_COLLECTION_NAME_USER = "user"
)
