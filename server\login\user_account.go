package login

import (
	"strings"

	slg "casrv/server/common"
	"casrv/server/common/ecode"
	ut "casrv/utils"
	"casrv/utils/sensitive"

	"github.com/huyangv/vmqant/log"
)

// 创建账号
func createUserAccount(loginType, openId, sessionKey, guestId, distinctId, platform, os, nickname, headicon, inviteUid, lang, ip string) (err string, uid string) {
	uid = genUID()
	if loginType == slg.LOGIN_TYPE_GUEST {
		nickname = nickname + uid
	} else if nickname == "" || !checkNickname(nickname) {
		nickname = "User" + uid
	} else {
		nickname = ut.TruncateString(strings.Trim(nickname, " "), 14)
	}
	data := lobby.NewTableData(uid, loginType, guestId, openId, sessionKey, nickname, headicon, distinctId, platform, os)
	if e := db.InsertOne(data); e != "" {
		log.Error("创建游客账号 err: %v", e)
		err = ecode.DB_ERROR.String()
	} else {
		_os := GetOs(os)
		log.Info("createUserAccount uid: %v, nickname: %v, loginType: %v, platform: %v, os: %v, _os: %v, ip: %v", uid, nickname, loginType, platform, os, _os, ip)
	}
	return
}

func GetOs(os string) string {
	if os == "" {
		return "none"
	}
	arr := strings.Split(os, ";")
	if len(arr) >= 1 {
		return arr[0]
	}
	return "none"
}

// 获取登录游戏的token
func getGameToken(uid string) (accountToken string) {
	// 先更新登录时间
	time := ut.Now()
	// 重新生成token
	str := uid + ut.Itoa(time)
	accountToken = ut.AESEncrypt(str)
	rds.RdsHSet(uid, rds.RDS_USER_FIELD_TOKEN, accountToken)
	return
}

// 生成唯一id
func genUID() string {
	uid := ut.UID8()
	for db.HasUid(uid) {
		uid = ut.UID8()
		log.Info("生成uid的时候发现一样的 uid=" + uid)
	}
	return uid
}

// 检测名字是否存在和合法
func checkNickname(nickname string) bool {
	if sensitive.CheckName(nickname) != 0 {
		return false // 是否合法
	}
	return !db.HasNickname(nickname)
}
