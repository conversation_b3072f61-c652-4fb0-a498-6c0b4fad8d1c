package lobby

import (
	g "casrv/server/common"
	"casrv/server/common/ecode"
	ut "casrv/utils"
	rds "casrv/utils/redis"
	"casrv/utils/sensitive"
	"time"

	"github.com/huyangv/vmqant/log"
)

var (
	users = ut.NewMapLock[string, *User]() // 当前所有连接的用户
)

// 检测名字是否存在和合法
func checkNickname(nickname string) bool {
	if sensitive.CheckName(nickname) != 0 {
		return false // 是否合法
	}
	return !db.HasNickname(nickname)
}

func GetUser(uid string) *User {
	if uid == "" {
		return nil
	}
	return users.Get(uid)
}

// 获取在线玩家
func GetUserByOnline(uid string) *User {
	user := GetUser(uid)
	if user != nil && user.IsOnline() {
		return user
	}
	return nil
}

// 解析token并获取user
func DecodeToken(token string, lobby *Lobby) (user *User, err string) {
	if token == "" {
		err = ecode.NOT_ACCOUNT_TOKEN.String()
		return
	}
	// 解析token
	str := ut.AESDecrypt(token)
	uid := str[0:8]
	// redis查询token验证
	rdsToken, e := rds.RdsHGet(uid, rds.RDS_USER_FIELD_TOKEN)
	if e != nil || token != rdsToken {
		log.Info("token无效")
		err = ecode.TOKEN_INVALID.String()
		return
	}
	user = GetUser(uid)
	if user == nil {
		// 内存中没有 先判断大厅服是否到达负载上限
		if len(users.Map) >= rds.LOBBY_ASSIGN_MAX_NUM {
			err = ecode.CUR_LOBBY_FULL.String()
			// 移除玩家lid
			rds.RdsHDel(rds.RDS_USER_LID_MAP_KEY, uid)
			return
		}
		// 从数据库获取
		data, e := db.FindByUid(uid)
		if e != "" {
			log.Info("token无效")
			err = ecode.TOKEN_INVALID.String()
			return
		}
		user = CreateUser(data)
		users.Lock()
		users.Map[data.Uid] = user
		users.Unlock()
		// 从数据库获取添加到内存 则更新大厅服负载
		rds.UpdateLobbyLoad(uid, lobby.GetLid(), true, true)
		log.Info("AddUser uid: %v, done.", data.Uid)
	} else if user.Session != nil {
		user.Kick(g.KICK_NOTIFY_TYPE_OTHER, 0)
		log.Info("AddUser uid: %v, kick!", user.UID)
	} else {
		log.Info("AddUser uid: %v, memory!", user.UID)
	}
	rds.RemUserMallocLidLock(uid)
	user.Init(lobby)
	return
}

// 获取登录游戏的token
func GetGameToken(user *User, ip string) (accountToken string) {
	// 先更新登录时间
	lastLoginTime := time.Now().UnixMilli()
	user.LastLoginTime = lastLoginTime
	user.Ip = ip
	user.SetDbUpdateFlag(1)
	// 重新生成token
	str := user.UID + ut.String(lastLoginTime)
	accountToken = ut.AESEncrypt(str)
	rds.RdsHSet(user.UID, rds.RDS_USER_FIELD_TOKEN, accountToken)
	return
}

// 保存玩家数据到db
func SaveUserDb(user *User) (del bool, now int64, err string) {
	// 先保存
	userDb := user.ToDB()
	err = db.UpdateData(user.UID, userDb)
	if err != "" {
		log.Error("SaveUserDb uid: %v, err: %v", user.UID, err)
		// 尝试保存每个字段
		for k, v := range userDb {
			db.UpdateOne(user.UID, k, v)
		}
	}
	now = time.Now().UnixMilli()
	// 如果没在线 并且可以更新 并且大厅服分配锁不生效 就直接删除
	if !user.IsOnline() && user.GetDbUpdateFlag() == 2 && !rds.GetUserMallocLidLock(user.UID) {
		exist := users.Get(user.UID) != nil
		users.Del(user.UID)
		// 从内存中移除则更新redis数据
		if exist {
			rds.UpdateLobbyLoad(user.UID, "", false, false)
		}
		del = true
	} else {
		user.SetDbLastUpdateTime(now)
		user.SetDbUpdateFlag(0)
	}
	return
}

// 保存所有用户
func SaveAllUser() {
	users.Lock()
	saveCount := 0
	userCount := len(users.Map)
	log.Info("SaveAllUser start userCount: %v", userCount)
	for _, user := range users.Map {
		db.UpdateData(user.UID, user.ToDB())
		user.Kick(g.KICK_NOTIFY_TYPE_NONE, 0)
		saveCount++
	}
	users.Map = map[string]*User{}
	users.Unlock()
	log.Info("SaveAllUser finish")
}
