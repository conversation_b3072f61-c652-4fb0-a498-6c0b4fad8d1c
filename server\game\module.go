package game

import (
	"casrv/server/common/ecode"
	"casrv/server/common/pb"
	ut "casrv/utils"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Game)
}

type Game struct {
	basemodule.BaseModule
}

func (this *Game) GetType() string {
	return "game"
}

func (this *Game) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Game) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Game) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	// id := settings.ID
	// this.BaseModule.OnInit(this, app, settings, server.ID(id))
	// this.GetServer().Options().Metadata["sid"] = strings.Replace(id, "game", "", 1) //设置元数据 方便路由的时候区分节点
	this.InitRpc()
	this.GetServer().RegisterGO("HD_Join", this.join)   //加入房间
	this.GetServer().RegisterGO("HD_Input", this.input) //玩家输入
}

func (this *Game) Run(closeSig chan bool) {
	// r.RunAllRoom(&this.BaseModule)
	GetRoom(1).Run()
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	// r.SaveAllRoom()
}

func (this *Game) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func checkError(session gate.Session) (string, *Room, *Player) {
	sid, uid := ut.Int(session.Get("sid")), session.GetUserID()
	room := GetRoom(sid)
	if room == nil {
		return ecode.ROOM_NOT_EXIST.String(), nil, nil
	} else if room.IsClose() {
		return ecode.ROOM_CLOSE.String(), nil, nil
	}
	player := room.GetPlayer(uid) //获取玩家
	if player == nil {
		return ecode.PLAYER_NOT_EXIST.String(), nil, nil
	}
	return "", room, player
}

// 加入房间
func (this *Game) join(session gate.Session, msg *pb.GAME_HD_JOIN_C2S) (ret []byte, err string) {
	room := GetRoom(1)
	player := room.PlayerJoin(msg.GetAccount())
	player.SetSession(session)
	session.Bind(player.UID)
	session.SetPush("sid", ut.String(room.ID))
	log.Info("player[%v:%v] join room[%v]", player.UID, player.PID, room.ID)
	// room.Run()
	return pb.ProtoMarshal(&pb.GAME_HD_JOIN_S2C{
		Pid:   int32(player.PID),
		State: room.gameSystem.State.ToState(),
	})
}

// 玩家输入
func (this *Game) input(session gate.Session, msg *pb.GAME_HD_INPUT_C2S) (ret []byte, err string) {
	err, room, player := checkError(session)
	if err != "" {
		return
	}
	room.PLayerInput(player, int(msg.GetSn()), msg.GetInputs(), int(msg.GetTime()))
	return
}
