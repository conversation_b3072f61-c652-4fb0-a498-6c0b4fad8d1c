package random

import (
	ut "casrv/utils"
	"math"
)

// 伪随机数
type Random struct {
	seed int //种子
}

func NewRandom(seed int) *Random {
	return &Random{
		seed: seed,
	}
}

func (this *Random) SetSeed(seed int) {
	this.seed = seed
}

func (this *Random) GetSeed() int {
	return this.seed
}

func (this *Random) Numn() float64 {
	this.seed = (this.seed*9301 + 49297) % 233280
	rand := math.Floor((float64(this.seed)/233280.0)*10000) * 0.0001
	return rand
}

func (this *Random) Get(min, max int) int {
	if min >= max {
		return min
	}
	return int(math.Floor(this.Numn()*float64(ut.Max(max-min, 0)+1))) + min
}

// 概率
func (this *Random) Chance(odds int) bool {
	mul := 100
	return odds > 0 && this.Get(0, 100*mul-1) < odds*mul
}
