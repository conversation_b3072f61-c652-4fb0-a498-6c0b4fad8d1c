package g

type UserTableData struct {
	Version   int32  `bson:"version"`
	Uid       string `bson:"uid"`
	Nickname  string `bson:"nickname"`
	Language  string `bson:"language"`   // 语言
	GuestId   string `bson:"guest_id"`   // 游客标识
	LoginType string `bson:"login_type"` // 登录类型
	Platform  string `bson:"platform"`   // 平台
	Ip        string `bson:"ip"`         // ip

	CreateTime        int64 `bson:"create_time"`          // 创建时间
	LastLoginTime     int64 `bson:"last_login_time"`      // 最后登陆时间
	LastOfflineTime   int64 `bson:"last_offline_time"`    // 最后离线时间
	SumOnlineTime     int64 `bson:"sum_online_time"`      // 累计在线时长
	NextToDayTime     int64 `bson:"next_today_time"`      // 明日开始时间
	BanAccountEndTime int64 `bson:"ban_account_end_time"` // 封禁账号结束时间

	BanAccountType    int32 `bson:"ban_account_type"`    // 封禁账号类型
	LoginDayCount     int32 `bson:"login_day_count"`     // 登录天数
	ContinueLoginDays int32 `bson:"continue_login_days"` // 连续登录天数
	RoleId            int32 `bson:"role_id"`             // 当前使用的角色id
}

func NewUserTableData(uid, loginType, guestId, nickname, platform string) UserTableData {
	return UserTableData{
		Uid:       uid,
		LoginType: loginType,
		GuestId:   guestId,
		Nickname:  nickname,
		Platform:  platform,
	}
}
