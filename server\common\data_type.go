package g

type UserTableData struct {
	Uid        string `bson:"uid"`
	Nickname   string `bson:"nickname"`
	HeadIcon   string `bson:"head_icon"`
	GuestId    string `bson:"guest_id"`    // 游客标识
	Openid     string `bson:"openid"`      // 微信openid
	SessionKey string `bson:"session_key"` // 会话密钥
	Account    string `bson:"account"`     // 账号
	Password   string `bson:"password"`    // 密码
	LoginType  string `bson:"login_type"`  // 登录类型
	DeviceOS   string `bson:"device_os"`   // 设备操作系统
	Platform   string `bson:"platform"`    // 平台
	Ip         string `bson:"ip"`          // ip
	Referrer   string `bson:"referrer"`    // 邀请者
}
