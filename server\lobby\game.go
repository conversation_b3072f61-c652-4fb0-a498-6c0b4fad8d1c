package lobby

import (
	ut "casrv/utils"
	rds "casrv/utils/redis"
)

type Item struct {
	Id int32 `json:"id"`
	Lv int8  `json:"lv"`
}

type Animal struct {
	Item
}

// 商店信息
type ShopInfo struct {
	Id    int32  `json:"id"`    //商店id
	Items []Item `json:"items"` //物品列表
}

// 其他玩家的信息
type PlayerInfo struct {
	Animals  []Animal `json:"animals"` //动物列表
	Bags     []Item   `json:"bags"`    //背包
	UID      string   `json:"uid"`
	Nickname string   `json:"nickname"`
	RoleId   int32    `json:"roleId"` //角色id
	Day      int16    `json:"day"`    //天数
}

// 游戏数据
type GameData struct {
	Animals     []*Animal   `json:"animals"`     //动物列表
	Bags        []*Item     `json:"bags"`        //背包
	CreateTime  int64       `json:"createTime"`  //创建时间
	Day         int16       `json:"day"`         //天数
	Shop        *ShopInfo   `json:"shopInfo"`    // 商店信息
	OtherPlayer *PlayerInfo `json:"otherPlayer"` //其他玩家信息
}

// 创建游戏信息
func CreateGame(user *User) *GameData {
	data := &GameData{
		CreateTime: ut.Now(),
		Day:        1, //从第一天开始
		Animals:    []*Animal{},
		Bags:       []*Item{},
	}
	// 随机三个东西
	data.Shop = &ShopInfo{}
	return data
}

// 获取游戏数据
func GetGameData(uid string) *GameData {
	data, err := rds.RdsHGetAll(rds.RDS_GAME_DATA_KEY)
	if err != nil {
		return nil
	}
	return data
}
