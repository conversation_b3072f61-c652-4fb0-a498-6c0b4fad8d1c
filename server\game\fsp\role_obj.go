package fsp

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
)

type RoleObj struct {
	PID      int
	Position *ut.Vec2 //位置
}

func NewRole(pid int, pos *pb.Vec2) *RoleObj {
	return &RoleObj{
		PID:      pid,
		Position: &ut.Vec2{X: int(pos.X), Y: int(pos.Y)},
	}
}

func (this *RoleObj) ToState() *pb.Role {
	pb.NewVec2(this.Position)
	return &pb.Role{
		Pid:      int32(this.PID),
		Id:       1001,
		Position: pb.NewVec2(this.Position),
		Dir:      1,
		State:    &pb.RoleStateInfo{Type: 0},
	}
}

// 移动
func (this *RoleObj) DoMove(speed *pb.Vec2) {
	this.Position.X += int(speed.X)
	this.Position.Y += int(speed.Y)
}

// 攻击
func (this *RoleObj) DoAttack(info *pb.AttackInfo) {

}

// 翻滚
func (this *RoleObj) DoRoll(dir *pb.Vec2) {

}
