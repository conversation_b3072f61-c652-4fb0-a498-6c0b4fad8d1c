package game

import (
	"casrv/server/common/pb"
	"casrv/server/game/fsp"
	ut "casrv/utils"
	"casrv/utils/tick"
	"sort"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type NotifyMsgInfo struct {
	player *Player
	body   []byte
}

type ClientInputInfo struct {
	Input *pb.GameSystemInput
	Time  int
}

type Room struct {
	ID        int
	isRunning bool
	tick      *tick.Tick

	gameSystem *fsp.GameSystem

	players       *ut.MapLock[string, *Player]
	pendingInputs []*ClientInputInfo
	lastSyncTime  int //最后一次同步时间
	nextPlayerId  int

	notifyMsg         chan *NotifyMsgInfo
	pendingInputMutex *deadlock.RWMutex
}

var rooms = ut.NewMapLock[int, *Room]()

func NewRoom(id int) *Room {
	room := &Room{
		ID:      id,
		players: ut.NewMapLock[string, *Player](),

		notifyMsg:         make(chan *NotifyMsgInfo),
		pendingInputMutex: new(deadlock.RWMutex),
	}
	return room
}

func GetRoom(id int) *Room {
	room := rooms.Get(id)
	if room == nil {
		room = NewRoom(id)
		room.pendingInputs = []*ClientInputInfo{}
		room.gameSystem = fsp.NewGameSystem()
		rooms.Set(id, room)
	}
	return room
}

func (this *Room) IsClose() bool {
	return false
}

func (this *Room) Run() {
	if this.isRunning {
		return
	}
	this.isRunning = true
	// this.NotifyAll("game/OnStart", nil)
	this.lastSyncTime = ut.Now()
	this.tick = tick.Run(1000/fsp.SYNC_RATE, this.onTick)
	this.CheckNotifyMsg()
	log.Info("room[%v] run.", this.ID)
}

func (this *Room) Stop() {
	this.isRunning = false
	if this.tick != nil {
		this.tick.Stop()
		this.tick = nil
	}
	log.Info("room[%v] stop.", this.ID)
}

func (this *Room) GetPlayer(uid string) *Player {
	return this.players.Get(uid)
}

func (this *Room) GetPlayerByCreate(uid string) *Player {
	plr := this.players.Get(uid)
	if plr == nil {
		plr = &Player{UID: uid}
		this.players.Set(uid, plr)
	}
	return plr
}

// 玩家加入
func (this *Room) PlayerJoin(uid string) *Player {
	this.nextPlayerId += 1
	player := this.GetPlayerByCreate(uid)
	player.PID = this.nextPlayerId
	this.ApplyInput(&pb.GameSystemInput{
		Cmd: fsp.FSPCMD_JOIN,
		Data_2: &pb.Role{
			Pid:      int32(player.PID),
			Id:       1001,
			Position: &pb.Vec2{X: int32(ut.Random(100, 200) * 1000), Y: int32(ut.Random(100, 200) * 1000)},
			Dir:      1,
			State:    &pb.RoleStateInfo{Type: 0},
		},
	})
	return player
}

// 玩家离线
func (this *Room) PlayerLeave(player *Player, close int) {
	this.players.Del(player.UID)
	this.ApplyInput(&pb.GameSystemInput{
		Cmd:    fsp.FSPCMD_LEAVE,
		Data_3: int32(player.PID),
	})
	// if this.players.Count() == 0 {
	// 	this.Stop()
	// }
	log.Info("player[%v:%v] leave room[%v]", player.UID, player.PID, this.ID)
}

// 玩家输入
func (this *Room) PLayerInput(player *Player, sn int, inputs []*pb.ClientInput, time int) {
	this.pendingInputMutex.Lock()
	for _, m := range inputs {
		this.pendingInputs = append(this.pendingInputs, &ClientInputInfo{
			Input: &pb.GameSystemInput{
				Pid:    int32(player.PID),
				Sn:     int32(sn),
				Cmd:    m.Cmd,
				Data_5: m.Data_5,
				Data_6: m.Data_6,
				Data_7: m.Data_7,
			},
			Time: time,
		})
	}
	this.pendingInputMutex.Unlock()
}

func (this *Room) ApplyInput(input *pb.GameSystemInput) {
	this.pendingInputMutex.Lock()
	this.pendingInputs = append(this.pendingInputs, &ClientInputInfo{Input: input, Time: ut.Now()})
	this.pendingInputMutex.Unlock()
}

// 每帧同步
func (this *Room) onTick(dt int, now int) bool {
	if !this.isRunning {
		return true
	}
	this.pendingInputMutex.Lock()
	// 先根据发送时间排个序
	sort.Slice(this.pendingInputs, func(i, j int) bool {
		return this.pendingInputs[i].Time < this.pendingInputs[j].Time
	})
	inputs := []*pb.GameSystemInput{}
	// str := ""
	for _, m := range this.pendingInputs {
		this.gameSystem.ApplyInput(m.Input)
		inputs = append(inputs, m.Input)
		// if m.Input.Sn >= 0 {
		// str += ut.String(m.LastSn) + ","
		// }
	}
	// if str != "" {
	// 	fmt.Println(str)
	// }
	// 发送同步帧
	this.players.ForEach(func(m *Player, uid string) bool {
		body, _ := pb.ProtoMarshal(&pb.GAME_ONFSPFRAME_NOTIFY{Inputs: inputs})
		this.notifyMsg <- &NotifyMsgInfo{player: m, body: body}
		return true
	})
	this.pendingInputs = []*ClientInputInfo{{
		Input: &pb.GameSystemInput{
			Cmd:    fsp.FSPCMD_TIME_PAST,
			Data_1: int32(now - this.lastSyncTime),
		},
		Time: 0,
	}}
	this.lastSyncTime = now
	this.pendingInputMutex.Unlock()
	return true
}

// 检测消息通知
func (this *Room) CheckNotifyMsg() {
	for this.isRunning {
		msg := <-this.notifyMsg
		if msg.player != nil {
			msg.player.SessionSendNR("game/OnFSPFrame", msg.body)
		}
	}
}

// 广播
func (this *Room) NotifyAll(topic string, body []byte, ignores ...string) {
	if !this.isRunning {
		return
	}
	this.players.ForEach(func(m *Player, uid string) bool {
		m.SessionSendNR(topic, body)
		return true
	})
}
