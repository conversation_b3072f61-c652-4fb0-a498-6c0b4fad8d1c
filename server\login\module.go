package login

import (
	g "casrv/server/common"
	"casrv/server/common/ecode"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Login)
}

type Login struct {
	basemodule.BaseModule
}

func (this *Login) GetType() string {
	return "login" //很关键,需要与配置文件中的Module配置对应
}

func (this *Login) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Login) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Login) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	this.GetServer().RegisterGO("HD_GuestLogin", this.guestLogin) // 游客登录
}

func (this *Login) Run(closeSig chan bool) {
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
}

func (this *Login) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 游客登录
func (this *Login) guestLogin(session gate.Session, msg *pb.LOGIN_HD_GUESTLOGIN_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid != "" {
		log.Warning("guestLogin repeat sessionId: %v, ip: %v", session.GetSessionID(), session.GetIP())
	}
	guestId, platform, os, nickName, inviteUid, lang := msg.GetGuestId(), msg.GetPlatform(), msg.GetOs(), msg.GetNickname(), msg.GetInviteUid(), msg.GetLang()
	needCreate := false
	if guestId != "" {
		// 从数据库查找是否有这个用户
		data, err := db.FindByLoginTypeOpenid(g.LOGIN_ID_TYPE_GUEST, guestId)
		if err != "" {
			needCreate = true
		} else {
			uid = data.Uid
			loginTrack(g.LOGIN_TYPE_GUEST, uid, distinctId, platform, lang, os)
		}
	}
	if guestId == "" || needCreate {
		guestId = ut.ID()
		headicon := g.FREE_HEAD_ICONS[ut.Random(0, len(g.FREE_HEAD_ICONS)-1)]
		err, uid = createUserAccount(g.LOGIN_TYPE_GUEST, "", "", guestId, distinctId, platform, os, nickName, headicon, inviteUid, lang, session.GetIP())
		if err != "" {
			err = ecode.DB_ERROR.String()
			return
		}
	}
	return pb.ProtoMarshal(&pb.LOGIN_HD_GUESTLOGIN_S2C{AccountToken: getGameToken(uid), GuestId: guestId})
}
