package fsp

import (
	"casrv/server/common/pb"
)

type GameSystem struct {
	State *GameState
}

func NewGameSystem() *GameSystem {
	return &GameSystem{State: NewGameState()}
}

// 应用输入，计算状态变更
func (this *GameSystem) ApplyInput(input *pb.GameSystemInput) {
	if input.Cmd == FSPCMD_MOVE { //移动
		if role := this.State.GetRole(int(input.Pid)); role != nil {
			role.DoMove(input.Data_5)
		}
	} else if input.Cmd == FSPCMD_ATTACK { //攻击
		if role := this.State.GetRole(int(input.Pid)); role != nil {
			role.DoAttack(input.Data_6)
		}
	} else if input.Cmd == FSPCMD_ROLL { //翻滚
		if role := this.State.GetRole(int(input.Pid)); role != nil {
			role.DoRoll(input.Data_7)
		}
	} else if input.Cmd == FSPCMD_JOIN { //加入
		data := input.Data_2
		this.State.AddRole(int(data.Pid), data.Position)
	} else if input.Cmd == FSPCMD_LEAVE { //离开
		this.State.RemoveRole(int(input.Data_3))
	} else if input.Cmd == FSPCMD_TIME_PAST { //时间流失
		this.State.Update(int(input.Data_1))
	}
}
